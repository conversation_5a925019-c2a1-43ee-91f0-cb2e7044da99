package com.innvoq.auth;

import com.innvoq.auth.service.RoleService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.method.ControllerAdviceBean;

@SpringBootApplication
@EnableAsync
@EnableScheduling
public class AuthApplication {

	public static void main(String[] args) {

		SpringApplication.run(AuthApplication.class, args);
		System.out.println("Loaded class from: " + ControllerAdviceBean.class.getProtectionDomain().getCodeSource().getLocation());
		System.out.println("Spring version: " + org.springframework.core.SpringVersion.getVersion());
	}

	@Bean
	public CommandLineRunner initializeRoles(RoleService roleService) {
		return args -> {
			roleService.initializeDefaultRoles();
		};
	}
}
